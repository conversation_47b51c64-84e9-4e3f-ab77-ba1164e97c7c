<mat-tree #tree [dataSource]="dataSource" [childrenAccessor]="childrenAccessor">
  <!-- Leaf node -->
  <mat-tree-node *matTreeNodeDef="let node" matTreeNodePadding>
    <div class="file-item">
      <mat-icon>table_chart</mat-icon>

      <div>{{ node.name }}</div>
      <div class="hidden-on-hover">
        <button class="view-file-btn" (click)="goToViewFile(node.id)">
          View File
        </button>
        <button
          mat-icon-button
          class="icon-button"
          (click)="deleteFile(node.id)">
          <mat-icon>delete_outline</mat-icon>
        </button>
      </div>
    </div>
  </mat-tree-node>

  <!-- Expandable node -->
  <mat-tree-node
    *matTreeNodeDef="let node; when: hasChild"
    matTreeNodePadding
    matTreeNodeToggle
    [cdkTreeNodeTypeaheadLabel]="node.name">
    <mat-icon>folder_open</mat-icon>
    <span class="folder-name">{{ node.name }}</span>

    <span class="folder-controls">
      <button mat-icon-button class="icon-button">
        <mat-icon>delete_outline</mat-icon>
      </button>
    </span>
    <button
      mat-icon-button
      matTreeNodeToggle
      class="arrow-button"
      (click)="getSubfolders(node.path)">
      <mat-icon>
        {{ tree.isExpanded(node) ? 'expand_more' : 'chevron_right' }}
      </mat-icon>
    </button>
  </mat-tree-node>

  <!-- Optional loader -->
  @if (isLoading) {
    <mat-progress-bar
      mode="indeterminate"
      class="example-tree-progress-bar"></mat-progress-bar>
  }
</mat-tree>
