import {
  Component,
  ChangeDetectionStrategy,
  Input,
  OnChanges,
  OnInit,
  inject,
  ChangeDetectorRef,
} from '@angular/core';

import { MatTreeModule } from '@angular/material/tree';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { CommonModule } from '@angular/common';
import { SharedModule } from '../../../../shared/shared.module';
import { MatExpansionModule } from '@angular/material/expansion';
import { DataViewComponentStore } from '../../component-store/data-view.store';
import { Router } from '@angular/router';
import { FileDirectoryEntry } from '../data-view.interfaces';

@Component({
  selector: 'app-data-view-tree',
  standalone: true,
  imports: [
    CommonModule,
    SharedModule,
    MatExpansionModule,
    MatIconModule,
    MatTreeModule,
    MatButtonModule,
    MatIconModule,
    MatProgressBarModule,
  ],
  templateUrl: './data-view-tree.component.html',
  styleUrl: './data-view-tree.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DataViewTreeComponent implements OnChanges, OnInit {
  dataSource: FileDirectoryEntry[] = [
    {
      name: '',
      children: [],
    },
  ];
  @Input() folderData: Record<number, FileDirectoryEntry[]> = [];
  isLoading = true;
  private store = inject(DataViewComponentStore);

  constructor(
    private cdr: ChangeDetectorRef,
    private router: Router,
  ) {}
  ngOnInit(): void {
    this.dataSource[0].name = this.store.project_id().toString();
  }

  ngOnChanges(): void {
    this.dataSource[0].children = this.folderData[this.store.project_id()];
    this.isLoading = this.store.isLoading();
    this.cdr.markForCheck();
  }

  childrenAccessor = (node: FileDirectoryEntry) => node.children ?? [];

  hasChild = (_: number, node: FileDirectoryEntry) =>
    (!!node.children && node.children.length > 0) || node.type == 'folder';

  getSubfolders(path: string) {
    this.store.setSelectedFolderPath(path);
    this.store.loadFiles(this.store.project_id(), path);
  }

  goToViewFile(id: number) {
    this.router.navigate([
      `/project/${this.store.project_id()}/data-view/${id}/file`,
    ]);
  }

  deleteFile(id: number) {
    this.store.deleteFile(id, this.store.project_id());
  }
}
