<div class="flex flex-row justify-between w-full px-6">
  <h2>Settings</h2>
  <div class="flex items-center space-x-4">
    <div>
      <app-search-header [filter]="false"></app-search-header>
    </div>
  </div>
</div>

<div class="grid grid-cols-4 gap-7 p-4">
  <!-- Left Sidebar -->
  <div class="col-span-1">
    <mat-nav-list>
      <mat-list-item
        class="rounded-none font-bold"
        [activated]="activeSection === 'loginSecurity'"
        (click)="changeSection('loginSecurity')">
        Login & Security
      </mat-list-item>
      <mat-divider class="border-gray-300"></mat-divider>

      <!-- Disabling Usage tab for now -->
      <mat-list-item
        class="rounded-none font-bold"
        [activated]="activeSection === 'usage'"
        (click)="changeSection('usage')">
        Usage
      </mat-list-item>
      <mat-divider class="border-gray-300"></mat-divider>

      <mat-list-item
        class="rounded-none font-bold"
        [activated]="activeSection === 'planBilling'"
        (click)="changeSection('planBilling')">
        Plan & Billing
      </mat-list-item>
    </mat-nav-list>
  </div>

  <!-- Main Content -->
  <div class="col-span-3 p-2">
    <!-- Login & Security Section -->
    <div *ngIf="activeSection === 'loginSecurity'">
      <h4>Your Profile</h4>
      <p class="text-customGray">You are signed in with this account:</p>
      <div class="flex justify-evenly items-center p-8 gap-4">
        <div
          class="flex items-center border px-3 bg-[#ffffff] w-full h-12 rounded-md">
          <input
            placeholder="User Email"
            class="outline-none bg-transparent flex-1"
            [(ngModel)]="email"
            readonly />
          <button mat-icon-button (click)="copyToClipboard(email)">
            <mat-icon>content_copy</mat-icon>
          </button>
        </div>
        <div
          class="flex items-center border px-3 py-2 bg-[#ffffff] w-full h-12 rounded-md">
          <input
            #password
            placeholder="User Password"
            class="outline-none bg-transparent flex-1"
            readonly />
          <button mat-icon-button (click)="copyToClipboard(password.value)">
            <mat-icon>content_copy</mat-icon>
          </button>
        </div>
        <button mat-icon-button class="text-black">
          <mat-icon>info</mat-icon>
        </button>
      </div>
      <h4>Modify</h4>
      <p class="text-customGray">Change your email or delete your account.</p>
      <div class="flex gap-3">
        <!-- Hiding change email button for this version -->
        <!-- <button
          mat-flat-button
          class="h-custom-Height bg-btn-color text-white hover:bg-[#1d4d7a]">
          <mat-icon>mail</mat-icon>
          Change Email
        </button> -->
        <button
          mat-flat-button
          class="h-custom-Height bg-transparent text-red-500"
          (click)="onDelete()">
          <mat-icon>delete_outline</mat-icon>
          Delete Account
        </button>
      </div>
    </div>

    <!-- Usage -->
    <div *ngIf="activeSection === 'usage'">
      <app-usage></app-usage>
    </div>

    <!-- Plan & Billing Section -->
    <div *ngIf="activeSection === 'planBilling'">
      <h4>Plan & Billing</h4>
      <p class="text-customGray">
        Change your plan and manage your billing settings
      </p>
      <div class="p-8 gap-4 w-4/5">
        <div class="flex flex-col gap-4">
          <p class="text-customGray">Your Plan</p>
          <div class="flex flex-wrap items-center justify-between">
            <div
              class="flex items-center justify-between border px-3 py-2 bg-[#ffffff] w-4/5 h-auto rounded-md">
              <div>
                <p class="m-0 text-sm">Basic Plan</p>
                <p class="m-0 text-sm text-customGray">
                  Best for personal projects with small datasets.
                </p>
              </div>
              <button mat-icon-button>
                <mat-icon>import_export</mat-icon>
              </button>
            </div>
            <button mat-icon-button class="justify-center">
              <mat-icon>info</mat-icon>
            </button>
          </div>
          @if (subscriptionNextPage === 'checkout') {
            <div class="flex flex-wrap items-center justify-between">
              <div
                class="flex items-center justify-between border px-3 py-2 bg-[#ffffff] w-4/5 h-auto rounded-md">
                <div>
                  <p class="m-0 text-sm">Upgrade Plan</p>
                  <p class="m-0 text-sm text-customGray">
                    Enjoy access to key features and benefits, with a plan
                    tailored for research & development to drive innovators
                    forward.
                  </p>
                </div>
                <button mat-icon-button>
                  <mat-icon>import_export</mat-icon>
                </button>
              </div>
              <button
                mat-flat-button
                (click)="openModal()"
                class="h-custom-Height bg-buttonBg text-white hover:bg-[#1d4d7a]">
                Learn More
              </button>
            </div>
          }
        </div>
        <div *ngIf="subscriptionNextPage !== 'checkout'" class="mt-[5%]">
          <p class="text-customGray">Billing</p>
          <div class="flex flex-wrap items-center justify-between">
            <div
              class="flex items-center justify-between border px-3 py-2 bg-[#ffffff] w-4/5 h-auto rounded-md">
              <div>
                <p class="m-0 text-sm">Billing Portal</p>
                <p class="m-0 text-sm text-customGray">
                  Manage payment methods & view previous invoices.
                </p>
              </div>
              <button mat-icon-button>
                <mat-icon>import_export</mat-icon>
              </button>
            </div>
            <button
              mat-flat-button
              class="h-custom-Height bg-buttonBg text-white hover:bg-[#1d4d7a]"
              (click)="goToBillingPortal()">
              Go to Portal
            </button>
          </div>
        </div>

        <div class="mt-[5%]">
          <p class="text-customGray">Additional Settings</p>
          <div class="flex flex-wrap items-center justify-between">
            <div
              class="flex items-center justify-between border px-3 py-2 bg-[#ffffff] w-4/5 h-auto rounded-md">
              <div>
                <p class="m-0 text-sm">Increase Plan Limits</p>
                <p class="m-0 text-sm text-customGray">
                  Add more files, training credits, and more on the usage page.
                </p>
              </div>
              <button mat-icon-button>
                <mat-icon>import_export</mat-icon>
              </button>
            </div>
            <button
              mat-flat-button
              class="h-custom-Height bg-buttonBg text-white hover:bg-[#1d4d7a]">
              Available Soon
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
