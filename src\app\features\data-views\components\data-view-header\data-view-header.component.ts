import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  HostListener,
  Input,
  Output,
  ViewChild,
} from '@angular/core';
import { g_const } from '../../../../_utility/global_const';

export interface SearchOptions {
  title?: string; // Optional property
  minDate?: string;
  maxDate?: string;
}

@Component({
  selector: 'app-data-view-header',
  templateUrl: './data-view-header.component.html',
  styleUrls: ['./data-view-header.component.css'],
  // eslint-disable-next-line @angular-eslint/prefer-standalone
  standalone: false,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DataViewHeaderComponent implements AfterViewInit {
  @Output() searchPerformed = new EventEmitter<SearchOptions>();
  @Output() isUploadModalEvent = new EventEmitter<boolean>();

  @Output() sortOptionChanged = new EventEmitter<string>(); // New Output for selectedSortOption
  @Input() projectName = '';
  @ViewChild('titleElement', { static: false }) titleElement!: ElementRef;

  openBuyPlanModal = false;
  isModalOpen = false;
  g_const = g_const;
  filename = '';
  screenWidth: number = window.innerWidth; // Initialize with current width
  showTooltip = true;
  selectedSortOption = 'initialSelect'; // Default selected option
  searchDate = ''; // Model for date input

  constructor(private cdr: ChangeDetectorRef) {
    this.updateScreenWidth(); // Initial update on load
  }
  ngAfterViewInit() {
    setTimeout(() => {
      const element = this.titleElement.nativeElement;
      this.showTooltip = element.scrollWidth > 450;
      this.cdr.detectChanges();
    }, 500);
  }

  // Listen to window resize events
  @HostListener('window:resize', ['$event'])
  onResize() {
    this.updateScreenWidth();
  }

  // Emit the selectedSortOption when it changes
  onSortOptionChange(selectedSortOption: string): void {
    if (selectedSortOption === '') {
      this.selectedSortOption = 'initialSelect'; // Reset the sort if "Remove sort" is selected
    } else {
      this.selectedSortOption = selectedSortOption;
    }
    this.sortOptionChanged.emit(this.selectedSortOption);
  }

  openModal() {
    this.isModalOpen = !this.isModalOpen;
  }

  onSearchPerformed(searchOptions: SearchOptions): void {
    this.searchPerformed.emit(searchOptions);
  }

  updateScreenWidth(): void {
    this.screenWidth = window.innerWidth;
  }

  eventPass(data: boolean) {
    this.isModalOpen = data;
    this.isUploadModalEvent.emit(data);
  }
}
