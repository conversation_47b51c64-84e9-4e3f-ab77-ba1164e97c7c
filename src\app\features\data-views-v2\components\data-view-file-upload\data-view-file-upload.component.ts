import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  <PERSON><PERSON><PERSON>,
  OnInit,
} from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import {
  MatDialogConfig,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { NgxFileDropEntry, NgxFileDropModule } from 'ngx-file-drop';
import { DataViewUploadTableHeading } from '../../shared/models';
import { UploadFilesTableComponent } from './upload-files-table/upload-files-table.component';
import { UploadPlaceholderComponent } from './upload-placeholder/upload-placeholder.component';
import { DataViewComponentStore } from '../../component-store/data-view.store';
import {
  convertFileEntryToFile,
  fileToBase64,
  getFileBlob,
  readFileAsArrayBuffer,
} from '../../shared/files.methods';
import J<PERSON>Z<PERSON> from 'jszip';
import {
  DataViewFile,
  DirectoryEntry,
  UploadedFile,
} from '../../shared/data-view.interfaces';

@Component({
  selector: 'app-data-view-file-upload',
  imports: [
    MatButtonModule,
    MatIconModule,
    MatDialogModule,
    UploadFilesTableComponent,
    NgxFileDropModule,
    UploadPlaceholderComponent,
  ],
  templateUrl: './data-view-file-upload.component.html',
  styleUrl: './data-view-file-upload.component.css',
  providers: [DataViewComponentStore],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DataViewFileUploadComponent implements OnInit {
  uploadedFilesList: {
    file: DataViewFile;
    progress: number;
    uploading: boolean;
  }[] = [];
  private rootDirectory: DirectoryEntry = {
    type: 'directory',
    name: 'root',
    children: [],
  };
  tableHeading = DataViewUploadTableHeading;
  readonly store = inject(DataViewComponentStore);
  readonly dialogRef = inject(MatDialogRef<DataViewFileUploadComponent>);

  static viewConfig: MatDialogConfig = {
    maxWidth: '100%',
    width: '860px',
    disableClose: true,
    maxHeight: '100%',
    height: '580px',
    panelClass: 'custom-dialog-container',
  };
  folderPath = '';

  constructor(
    private cdr: ChangeDetectorRef,
    private ngZone: NgZone,
  ) {}
  ngOnInit(): void {
    this.folderPath = this.store.selectedFolderPath();
    console.log('init', this.folderPath);
  }

  onFileInputChange() {
    this.cdr.markForCheck();
  }

  dropped(files: NgxFileDropEntry[]) {
    this.resetState();

    const directoryFiles = this.filterFilesByDirectory(files);
    const zipFile = this.filterFilesByZip(files);

    if (directoryFiles.length > 0) {
      this.processIndividualFiles(directoryFiles);
    } else if (zipFile.length > 0) {
      this.processZipFile(zipFile[0]);
    } else {
      this.processIndividualFiles(files);
    }
  }

  filterFilesByDirectory(files: NgxFileDropEntry[]): NgxFileDropEntry[] {
    return files.filter(
      file =>
        file.relativePath &&
        file.relativePath.includes('/') &&
        file.fileEntry.isFile,
    );
  }

  private filterFilesByZip(files: NgxFileDropEntry[]): NgxFileDropEntry[] {
    return files.filter(file => /\.zip$/i.test(file.relativePath));
  }

  private async processZipFile(file: NgxFileDropEntry): Promise<void> {
    const fileEntry = file.fileEntry as FileSystemFileEntry;

    fileEntry.file(async (zipFile: File) => {
      try {
        const arrayBuffer = await readFileAsArrayBuffer(zipFile);
        const zip = await JSZip.loadAsync(arrayBuffer);

        const dataViewFiles: DataViewFile[] = [];

        // Loop through all entries in the ZIP
        const zipEntries = Object.values(zip.files); // Ensure sequential iteration

        for (const zipEntry of zipEntries) {
          if (!zipEntry.dir) {
            const blob = await zipEntry.async('blob');
            const unzippedFile = new File(
              [blob],
              zipEntry.name.split('/').pop() ?? '',
              {
                type: blob.type,
              },
            );

            const dataViewFile: DataViewFile = {
              file: unzippedFile,
              fullPath: zipEntry.name.includes('/')
                ? zipEntry.name.substring(0, zipEntry.name.lastIndexOf('/'))
                : zipEntry.name,
            };

            dataViewFiles.push(dataViewFile);

            console.log(zipEntry, 'zip entry');
            // Optionally update visual tree
            const fileEntry = {
              type: 'file' as const,
              name: zipEntry.name.split('/').pop() || zipEntry.name,
              fullPath: dataViewFile.fullPath,
              path: await fileToBase64(unzippedFile),
            };

            this.rootDirectory.children.push(fileEntry);
          }
        }

        // Now pass all extracted files to your existing addFilesToList function
        this.addFilesToList(dataViewFiles);
      } catch (error) {
        console.error('❌ Failed to process zip file:', error);
      }
    });
  }

  processIndividualFiles(files: NgxFileDropEntry[]): void {
    const fileEntries = files.map(
      file => file.fileEntry as FileSystemFileEntry,
    );

    Promise.all(fileEntries.map(fileEntry => convertFileEntryToFile(fileEntry)))
      .then(convertedFiles => {
        this.addFilesToList(convertedFiles);
        this.ngZone.run(() => {
          this.cdr.markForCheck();
        });
      })
      .catch(error => {
        console.error('Error processing dropped files:', error);
        // this.toastrService.error('Error processing dropped files');
      });
  }

  closeModal() {
    this.dialogRef.close();
    this.store.resetState();
  }

  uploadDirectoryStructureOrZipFile(file: File) {
    console.log(file);
  }

  private resetState() {
    this.store.setfileUploadLoading(true);
    this.rootDirectory.children = [];
  }

  openFileSelector() {
    console.log('open file selector');
  }

  onFileRemoved(index: number) {
    // Remove from component store
    this.store.removeUploadedFile(index);

    // Update local array to reflect the change
    this.uploadedFilesList = this.store.uploadedFilesList();

    // Remove from root directory children if it exists
    if (this.rootDirectory.children.length > index) {
      this.rootDirectory.children = [
        ...this.rootDirectory.children.slice(0, index),
        ...this.rootDirectory.children.slice(index + 1),
      ];
    }

    // Trigger change detection
    this.cdr.markForCheck();
  }

  addFilesToList(files: DataViewFile | DataViewFile[] | DataViewFile) {
    console.log(files, 'files');
    const uploadedFilesList: UploadedFile[] = this.store.uploadedFilesList();
    // Convert the FileList or single file to an array if needed
    const fileArray: DataViewFile[] =
      files instanceof FileList
        ? Array.from(files).map(file => ({
            file,
            fullPath:
              file.webkitRelativePath.substring(
                0,
                file.webkitRelativePath.lastIndexOf('/'),
              ) ?? file.name, // fallback if no path
          }))
        : Array.isArray(files)
          ? (files as DataViewFile[])
          : [files as DataViewFile];

    // Handle each file
    fileArray.forEach(file => {
      // Skip if file is already in the list (avoid duplicates)
      const fileAlreadyExists = uploadedFilesList.some(
        item =>
          item.file.file.name === file.file.name &&
          item.file.file.size === file.file.size,
      );

      if (fileAlreadyExists) {
        return;
      }

      // Add the file to the list with initial progress of 0
      uploadedFilesList.push({
        file,
        progress: 0,
        uploading: false,
      });
      this.store.setUploadedFiles(uploadedFilesList);
      this.uploadedFilesList = this.store.uploadedFilesList();
      this.cdr.markForCheck();
    });
  }

  uploadFiles() {
    if (this.uploadedFilesList.length === 0) {
      return;
    }

    this.processRegularFiles(this.uploadedFilesList);
  }

  async processRegularFiles(
    regularFiles: typeof this.uploadedFilesList,
  ): Promise<void> {
    if (regularFiles.length === 0) return;
    const fileBlob: Blob[] = [];
    try {
      // Convert all regular files to base64 and update state
      for (const fileItem of regularFiles) {
        await this.processSingleRegularFile(fileItem);
      }

      for (const fileEntry of this.rootDirectory.children) {
        console.log(fileEntry.type);
        if (fileEntry.type === 'file') {
          const blob = (await getFileBlob(fileEntry)) as Blob;
          fileBlob.push(blob);
          if (!fileBlob) {
            console.error('Failed to get file blob for', fileEntry.name);
            continue;
          }
        } else {
          fileBlob.push(new Blob([], { type: 'application/octet-stream' }));
        }
      }

      if (this.rootDirectory.children.length > 0) {
        regularFiles.forEach(item => (item.progress = 80));

        try {
          // await this.uploadFileStructure(fileSize);
          console.log(this.folderPath, 'folder path');
          this.store.getPresignedUrl({
            fileBlobs: fileBlob,
            folderPath: this.store.selectedFolderPath(),
          });
          regularFiles.forEach(item => (item.progress = 100));
        } catch (uploadError) {
          console.error('DEBUG: Error uploading regular files:', uploadError);
        }
      }
    } catch (batchError) {
      console.error(
        'DEBUG: Error in batch processing regular files:',
        batchError,
      );
    }
  }

  private async processSingleRegularFile(
    fileItem: UploadedFile,
  ): Promise<void> {
    const file = fileItem.file.file;
    fileItem.uploading = true;
    fileItem.progress = 10;

    try {
      const base64 = await fileToBase64(file);
      fileItem.progress = 50;

      const fileEntry = {
        type: 'file' as const,
        name: file.name,
        path: base64,
      };

      this.rootDirectory.children.push(fileEntry); // ✅ direct mutation

      fileItem.progress = 70;
    } catch (error) {
      console.error(`DEBUG: Error processing file ${file.name}:`, error);
      fileItem.progress = 0;
      throw error;
    }
  }
}
