import { computed, inject } from '@angular/core';
import {
  catchError,
  EMPTY,
  firstValueFrom,
  forkJoin,
  from,
  interval,
  of,
  pipe,
  switchMap,
  takeWhile,
  tap,
} from 'rxjs';
import {
  patchState,
  signalStore,
  withComputed,
  withHooks,
  withMethods,
  withState,
} from '@ngrx/signals';
import { withDevtools } from '@angular-architects/ngrx-toolkit';
import { DataViewServiceV2 } from '../services/data-view.service';
import { rxMethod } from '@ngrx/signals/rxjs-interop';
import { getFileBlob, updateHierarchy } from '../shared/files.methods';
import {
  DataViewResponse,
  DirectoryEntry,
  FileDirectoryEntry,
  PresignedUrlResponse,
  UploadedFile,
} from '../shared/data-view.interfaces';

export const DataViewInitialState: DataViewComponentState = {
  hierarchy: {},
  isLoading: true,
  selectedFolderPath: '',
  uploadedFilesList: [],
  fileUploadLoading: false,
  toastShown: false,
  project_id: 0,
  sort_value: '',
  search: '',
  rootDirectory: {
    type: 'directory',
    name: 'root',
    children: [],
  },
};

export interface DataViewComponentState {
  hierarchy: Record<number, FileDirectoryEntry[]>;
  isLoading: boolean;
  uploadedFilesList: UploadedFile[];
  rootDirectory: DirectoryEntry;
  selectedFolderPath: string;
  fileUploadLoading: boolean;
  project_id: number;
  sort_value: string;
  toastShown: boolean;
  search: string;
}

export const DataViewComponentStore = signalStore(
  withState<DataViewComponentState>(DataViewInitialState),
  withDevtools('DataViewStore'),

  withMethods((store, dataViewService = inject(DataViewServiceV2)) => ({
    async loadFiles(projectId: number, path = '') {
      this.setLoading(true);
      // const existingHierarchy = store.hierarchy()[projectId];
      // if (existingHierarchy) {
      //   const pathParts = path.split('/').filter(Boolean);

      //   let currentLevel = existingHierarchy;
      //   let found = true;

      //   for (const part of pathParts) {
      //     const folderNode = currentLevel.find(
      //       (node: FileDirectoryEntry) =>
      //         node.type === 'folder' && node.name === part,
      //     );
      //     if (!folderNode) {
      //       found = false;
      //       break;
      //     }
      //     currentLevel = folderNode.children ?? [];
      //   }

      // if (found && currentLevel && currentLevel.length > 0) {
      //   console.log('Children already loaded for path:', path);
      //   return;
      // }
      // }

      try {
        const files = await firstValueFrom(
          dataViewService.getFilesList(
            projectId,
            1,
            10,
            path,
            store.sort_value(),
            store.search(),
          ),
        );
        const hierarchy: FileDirectoryEntry[] = [];
        files.data.files.forEach(
          (file: { name: string; id: number; folder: string }) => {
            hierarchy.push({
              name: file.name,
              path: file.folder,
              type: 'file',
              id: file.id,
            });
          },
        );
        const folder = files.data.folder;
        files.data.subfolders.forEach((folderName: string) => {
          hierarchy.push({
            name: folderName,
            path: folder ? `${folder}/${folderName}` : folderName,
            type: 'folder',
            children: [],
          });
        });
        const existingFiles = store.hierarchy()[projectId];
        if (existingFiles) {
          const updatedHierarchy = updateHierarchy(
            existingFiles,
            path,
            hierarchy,
          );
          this.setFiles(updatedHierarchy, projectId);
        } else {
          this.setFiles(hierarchy, projectId);
        }
        this.setLoading(false);
      } catch (error) {
        console.error('Failed to load files:', error);
      }
    },
    getFileBlobsd() {
      return from(
        Promise.all(
          store.uploadedFilesList().map(file => getFileBlob(file.file.file)),
        ),
      ).pipe(
        tap((fileBlobs: (Blob | null)[]) => {
          console.log(fileBlobs);
        }),
      );
    },

    getPresignedUrl: rxMethod<{ fileBlobs: Blob[]; folderPath: string }>(
      pipe(
        tap(() => patchState(store, { isLoading: true })),
        switchMap(({ fileBlobs, folderPath }) => {
          // const currentFolderPath = store.selectedFolderPath();

          const payload = store.uploadedFilesList().map(file => ({
            file_name: file.file.file.name,
            size: file.file.file.size,
          }));

          return dataViewService.presignedUrl(payload).pipe(
            switchMap((response: DataViewResponse<PresignedUrlResponse>) => {
              const fileKeys = response.data.map(item => item.file_key);
              const signedUrl = response.data.map(item => item.signed_url);
              const updatedUploadedFiles = store
                .uploadedFilesList()
                .map((file, index) => ({
                  ...file,
                  file_key: fileKeys[index],
                }));
              const blobRequests = fileBlobs.map((blob: Blob, index) => {
                const url = signedUrl[index];
                return dataViewService.signedUrlUpload(url, blob).pipe(
                  catchError(error => {
                    console.error(`Upload failed for file :`, error);
                    return of(null);
                  }),
                );
              });

              //Wait for all to complete and collect results
              // return forkJoin(blobRequests)
              return forkJoin(blobRequests).pipe(
                // After all uploads succeed, call process API for each file_key
                switchMap(() => {
                  console.log(folderPath, 'folderpath');
                  const processObservables = updatedUploadedFiles.map(item =>
                    dataViewService
                      .processFile({
                        file_name: item.file.file.name,
                        file_key: item.file_key,
                        folder: store.selectedFolderPath()
                          ? `${store.selectedFolderPath()}/${item.file.fullPath}`
                          : item.file.fullPath || '',
                        project_id: store.project_id(),
                        size: item.file.file.size,
                      })
                      .pipe(
                        // Poll every 2 seconds until status becomes 'success'
                        switchMap(response => {
                          const taskId = response.data.task_id;
                          return interval(2000).pipe(
                            switchMap(() =>
                              dataViewService.getFileProcessStatus(taskId),
                            ),
                            takeWhile(
                              statusResponse =>
                                statusResponse.data.status !== 'SUCCESS',
                              true,
                            ),
                            catchError(error => {
                              console.error(
                                `Error polling status for ${item.file_key}:`,
                                error,
                              );
                              return of(null); // Let other observables continue
                            }),
                          );
                        }),
                        catchError(error => {
                          console.error(
                            `Error processing file ${item.file_key}:`,
                            error,
                          );
                          return of(null);
                        }),
                      ),
                  );

                  // Wait for all process + polling status calls to finish
                  return forkJoin(processObservables);
                }),
              );
            }),
            tap(finalUrls => {
              console.log('Final Signed URLs', finalUrls);
              patchState(store, { isLoading: false });
            }),
            catchError(err => {
              console.error('Failed in presigned URL flow', err);
              patchState(store, { isLoading: false });
              return EMPTY;
            }),
          );
        }),
      ),
    ),

    deleteFile(id: number, project_id: number) {
      return dataViewService.DeleteFiles(id).subscribe(() => {
        this.loadFiles(project_id, '');
      });
    },

    setFiles(files: FileDirectoryEntry[], projectId: number): void {
      patchState(store, state => ({
        ...state,
        hierarchy: {
          ...state.hierarchy,
          [projectId]: files,
        },
      }));
    },

    setLoading(isLoading: boolean): void {
      patchState(store, state => ({
        ...state,
        isLoading: isLoading,
      }));
    },

    setSortValue(sortValue: string) {
      patchState(store, state => ({
        ...state,
        sort_value: sortValue,
      }));
    },

    setSearchTerm(searchTxt: string) {
      patchState(store, state => ({
        ...state,
        search: searchTxt,
      }));
    },

    setUploadedFiles(files: UploadedFile[]): void {
      patchState(store, state => ({
        ...state,
        uploadedFilesList: files,
      }));
    },

    setfileUploadLoading(isLoading: boolean): void {
      patchState(store, state => ({
        ...state,
        fileUploadLoading: isLoading,
      }));
    },

    setSelectedFolderPath(path: string) {
      patchState(store, state => ({
        ...state,
        selectedFolderPath: path,
      }));
    },

    setProjectDetails(project_id: number) {
      patchState(store, state => ({
        ...state,
        project_id: project_id,
      }));
    },

    resetState() {
      patchState(store, () => ({
        ...DataViewInitialState,
      }));
    },
  })),

  withHooks({
    onInit(store) {
      store.setProjectDetails(Number(localStorage.getItem('project_id')));
      store.loadFiles(store.project_id());
    },
  }),

  withComputed(store => ({
    hierarchy_output: computed(() => store.hierarchy()),
  })),
);
