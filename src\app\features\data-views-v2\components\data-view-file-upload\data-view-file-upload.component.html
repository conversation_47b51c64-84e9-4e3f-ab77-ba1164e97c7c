<div class="flex justify-between p-4 items-center dialog-modal">
  <h3 mat-dialog-title class="text-lg font-medium">Add Data</h3>
  <button
    mat-icon-button
    class="text-setSettinggray-400 hover:text-gray-600"
    (click)="closeModal()">
    <mat-icon>close</mat-icon>
  </button>
</div>

<hr />

<mat-dialog-content class="max-h-[300px]">
  <input
    #fileInput
    type="file"
    multiple
    accept=".csv,.parquet,.jpg,.jpeg,.png"
    class="hidden"
    (change)="onFileInputChange()" />

  <ngx-file-drop
    dropZoneLabel="Drop files or folders here"
    [multiple]="true"
    (onFileDrop)="dropped($event)">
    <ng-template
      ngx-file-drop-content-tmp
      let-openFileSelector="openFileSelector">
      <div class="flex-grow overflow-y-auto p-4 content-container">
        @if (!uploadedFilesList.length) {
          <app-upload-placeholder
            [openFileSelector]="openFileSelector"></app-upload-placeholder>
        }

        <!-- Empty state - shown when no files are selected -->
        @if (!uploadedFilesList || uploadedFilesList.length === 0) {
          <div class="text-center p-4 text-gray-500">No files selected</div>
        }

        <!-- Uploaded files list -->
        @if (uploadedFilesList && uploadedFilesList.length > 0) {
          <div class="uploaded-files-section">
            <div class="flex justify-between items-center mb-4">
              <h4>
                Uploaded Files ({{
                  uploadedFilesList ? uploadedFilesList.length : 0
                }})
              </h4>
            </div>

            <!-- File list with table layout -->
            <div class="mb-4">
              @if (uploadedFilesList && uploadedFilesList.length > 0) {
                <app-upload-files-table
                  [uploadedFilesList]="uploadedFilesList"
                  (fileRemoved)="onFileRemoved($event)"></app-upload-files-table>
              }
            </div>

            <div class="flex justify-center">
              <button
                mat-flat-button
                (click)="openFileSelector()"
                class="add-more-button">
                <mat-icon class="mr-1">add</mat-icon> Add More Files
              </button>
            </div>
          </div>
        }
      </div>
    </ng-template>
  </ngx-file-drop>
</mat-dialog-content>

<mat-dialog-actions>
  <div class="action-buttons">
    <button
      mat-button
      (click)="closeModal()"
      class="secondary-button"
      aria-label="Cancel and close modal">
      Cancel
    </button>
    <button
      mat-flat-button
      [disabled]="!uploadedFilesList.length"
      (click)="uploadFiles()"
      class="primary-button"
      aria-label="Upload selected files">
      Upload
    </button>
  </div>
</mat-dialog-actions>
