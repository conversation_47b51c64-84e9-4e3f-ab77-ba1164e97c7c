::ng-deep .custom-dialog-container .mat-mdc-dialog-surface {
  border-radius: 16px !important;
  background: white;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

::ng-deep .custom-dialog-container .mat-mdc-dialog-content {
  padding: 0 !important;
  margin: 0 !important;
  max-height: none !important;
}

.dialog-modal {
  background: white;
  border-bottom: 1px solid #e9ecef;
  padding: 24px;
}

.dialog-modal h3 {
  font-family: 'Roboto', sans-serif;
  font-weight: 400;
  font-size: 22px;
  line-height: 28px;
  color: #181C20;
  margin: 0;
}

.content-container {
  padding: 0;
  margin: 0;
  width: 100%;
  height: 100%;
  display: block;
  color: #000000;
  background: white;
}

ngx-file-drop {
  width: 100%;
  height: 100%;
}

.uploaded-files-section {
  padding: 16px 24px;
}

.uploaded-files-section h4 {
  font-family: 'Roboto', sans-serif;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  color: #181C20;
  margin: 0 0 16px 0;
}

::ng-deep .mat-mdc-dialog-actions {
  padding: 24px !important;
  border-top: 1px solid #e9ecef;
  background: white;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  width: 100%;
}

.action-buttons button {
  font-family: 'Roboto', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  padding: 8px 16px;
  border-radius: 4px;
}

.primary-button {
  background-color: #296187 !important;
  color: white !important;
}

.primary-button:disabled {
  background-color: #e9ecef !important;
  color: #73777F !important;
}

.secondary-button {
  background-color: transparent !important;
  color: #73777F !important;
  border: 1px solid #e9ecef !important;
}

.add-more-button {
  background-color: #296187 !important;
  color: white !important;
  font-family: 'Roboto', sans-serif;
  font-weight: 500;
  font-size: 14px;
}
