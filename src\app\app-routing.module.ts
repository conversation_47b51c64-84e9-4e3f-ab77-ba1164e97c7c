import { inject, NgModule } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  ResolveFn,
  RouterModule,
  Routes,
} from '@angular/router';
import { OuterLayoutComponent } from './shared/layout/outer-layout/outer-layout.component';
import { OverviewService } from './services/overview.service';
import { ProjectDataDetails } from './features/dashborad/models/project.model';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ActivateGuardGuard } from './core/guards/activate-guard.guard';
import { AuthGuard } from './core/guards/auth.guard';

const projectDetailsResolver: ResolveFn<ProjectDataDetails> = (
  route: ActivatedRouteSnapshot,
) => inject(OverviewService).getProjectsById(route.params['id']);

export const routes: Routes = [
  {
    path: 'auth',
    loadChildren: () =>
      import('./features/auth/auth.module').then(m => m.AuthModule),
  },
  {
    path: 'activate-account/:uid/:token',
    loadChildren: () =>
      import(
        './features/auth/components/account-activation/account-activation.module'
      ).then(m => m.AccountActivationModule),
    canActivate: [ActivateGuardGuard],
  },
  {
    path: '',
    component: OuterLayoutComponent,
    children: [
      {
        path: 'dashboard',
        loadChildren: () =>
          import('./features/dashborad/dashboard.module').then(
            m => m.DashboardModule,
          ),
      },
      {
        path: 'project',
        loadChildren: () =>
          import('./features/overview/overview.module').then(
            m => m.OverviewModule,
          ),
      },
      {
        path: 'project/:id',
        loadChildren: () =>
          import('./features/data-views/data-view.module').then(
            m => m.DataViewModule,
          ),
      },
      {
        path: 'project/:id/data-view-v2',
        loadChildren: () =>
          import('./features/data-views-v2/data-view-v2.module').then(
            m => m.DataViewV2Module,
          ),
      },
      // {
      //   path: 'project/:id/data-version',
      //   loadChildren: () =>
      //     import('./components/data-version/data-version.module').then(
      //       m => m.DataVersionModule,
      //     ),
      // },
      {
        path: 'settings',
        loadChildren: () =>
          import('./features/settings/settings.module').then(
            m => m.SettingsModule,
          ),
      },
      {
        path: 'project/:id/data-insights',
        loadChildren: () =>
          import('./features/visual-data/visual-data.module').then(
            m => m.VisualDataModule,
          ),
        resolve: { projectDetails: projectDetailsResolver },
      },
      {
        path: 'project/:id/data-insights-v2',
        loadChildren: () =>
          import('./features/visual-data-v2/visual-data-v2.module').then(
            m => m.VisualDataV2Module,
          ),
        resolve: { projectDetails: projectDetailsResolver },
      },
      {
        path: 'project/:id',
        loadChildren: () =>
          import('./features/training/training.module').then(
            m => m.TrainingModule,
          ),
      },

      { path: '', redirectTo: '/dashboard', pathMatch: 'full' },
    ],
    canActivate: [AuthGuard],
  },
  { path: '', redirectTo: '/dashboard/projects', pathMatch: 'full' },
];

@NgModule({
  imports: [RouterModule.forRoot(routes), BrowserAnimationsModule],
  exports: [RouterModule],
})
export class AppRoutingModule {}
