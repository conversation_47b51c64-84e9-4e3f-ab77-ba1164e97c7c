<app-loader [loading]="isComponentLoaded"></app-loader>
<app-data-view-header
  (isUploadModalEvent)="afterUpload()"
  [projectName]="projectName"
  (searchPerformed)="onSearchPerformed($event)"
  (sortOptionChanged)="handleSortOptionChange($event)"></app-data-view-header>

<div class="flex flex-row mt-[28px]">
  <div class="w-full overflow-y-auto max-h-[80vh]">
    <mat-accordion class="w-full">
      <ng-container *ngFor="let folder of data">
        <app-folder-panel
          [folder]="folder"
          [rootFolderId]="folder.id"
          [showTable]="showTable.bind(this)"
          [viewFile]="viewFile.bind(this)"
          [deleteFile]="DeleteFile.bind(this)"
          [deleteFolder]="DeleteFolder.bind(this)"
          [PreviewFiles]="PreviewFiles.bind(this)"
          [sortOption]="sortOption">
        </app-folder-panel>
      </ng-container>
    </mat-accordion>
  </div>

  <!-- Table Section -->
  <div
    class="overflow-y-auto hover:overflow-y-auto overflow-hidden ml-6 table-modal rounded-lg border-gray-400 shadow-lg max-h-[80vh] min-w-[25vw] relative"
    *ngIf="isTableVisible || isImageVisible">
    <div
      *ngIf="previewLoading"
      class="absolute inset-0 flex justify-center items-center bg-white bg-opacity-80 backdrop-blur-sm z-10">
      <app-loader [loading]="previewLoading"></app-loader>
    </div>

    <p class="font-medium">Preview</p>

    <section
      class="example-container mat-elevation-z8 min-w-[25vw] border border-[#ecebf8] rounded-md"
      tabindex="0"
      *ngIf="isTableVisible">
      <table mat-table [dataSource]="dataSource" class="min-w-[100%]">
        <ng-container
          *ngFor="let column of displayedColumns"
          [matColumnDef]="column">
          <th mat-header-cell *matHeaderCellDef class="">
            {{ column | titlecase }}
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element[column] }}
          </td>
        </ng-container>
        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      </table>
    </section>

    <section class="mat-elevation-z8" tabindex="0" *ngIf="isImageVisible">
      <img [src]="safeFileUrl" alt="File Preview" />
    </section>

    <div>
      <p class="font-medium mt-4">
        {{ fileName | capitalize | replaceUnderscore }}
      </p>
      <p class="m-0" *ngIf="isTableVisible">
        {{ numColumns }} columns, {{ numRows }} rows
      </p>
      <p class="m-0">File Size: {{ fileSize }}</p>
      <p *ngIf="isTableVisible" class="mt-4">Columns</p>

      <span *ngIf="isTableVisible">
        <mat-chip-set class="m-0 gap-2">
          <mat-chip *ngFor="let column of displayedColumns.slice(0, 6)">
            {{ column }}
          </mat-chip>
          <ng-container *ngIf="displayedColumns.length > 6 && !showAllChips">
            <mat-chip (click)="showAllChips = true" class="cursor-pointer">
              +{{ displayedColumns.length - 6 }} more
            </mat-chip>
          </ng-container>
          <ng-container *ngIf="showAllChips">
            <mat-chip *ngFor="let column of displayedColumns.slice(6)">
              {{ column }}
            </mat-chip>
          </ng-container>
        </mat-chip-set>
      </span>

      <p *ngIf="isImageVisible">{{ resolution }}</p>
    </div>
  </div>
</div>
