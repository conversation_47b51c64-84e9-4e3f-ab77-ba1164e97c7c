import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../env/env';
import {
  DataviewResponse,
  DataViewResponse,
  PresignedUrlPayload,
  PresignedUrlResponse,
  ProcessFilePayload,
  ProcessFileResponse,
  ProcessStatusResponse,
} from '../shared/data-view.interfaces';

@Injectable({
  providedIn: 'root',
})
export class DataViewServiceV2 {
  constructor(private http: HttpClient) {}

  private getHeaders(): HttpHeaders {
    const token = localStorage.getItem('access_token');
    return new HttpHeaders({
      Authorization: `Bearer ${token || ''}`,
      'Content-Type': 'application/json',
    });
  }

  getFilesList(
    project_id: number,
    page = 1,
    page_size = 10,
    path = '',
    sort: string,
    search: string,
  ): Observable<DataviewResponse> {
    const params = new HttpParams()
      .set('page', page.toString())
      .set('page_size', page_size.toString());

    return this.http.get<DataviewResponse>(
      `${environment.apiUrl}files/folders/?project_id=${project_id}&folder_name=${path}&search=${search}&sort_by=${sort}&sort_order=desc`,
      {
        headers: this.getHeaders(),
        params: params,
      },
    );
  }

  presignedUrl(
    payload: PresignedUrlPayload[],
  ): Observable<DataViewResponse<PresignedUrlResponse>> {
    return this.http.post<DataViewResponse<PresignedUrlResponse>>(
      `${environment.apiUrl}files/presigned-url/`,
      payload,
      {
        headers: this.getHeaders(),
      },
    );
  }

  processFile(payload: ProcessFilePayload): Observable<ProcessFileResponse> {
    return this.http.post<ProcessFileResponse>(
      `${environment.apiUrl}files/`,
      payload,
      { headers: this.getHeaders() },
    );
  }

  getFileProcessStatus(task_id: string): Observable<ProcessStatusResponse> {
    return this.http.get<ProcessStatusResponse>(
      `${environment.apiUrl}files/status/${task_id}/`,
      { headers: this.getHeaders() },
    );
  }

  signedUrlUpload(signedUrl: string, binaryData: Blob): Observable<null> {
    return this.http.put<null>(signedUrl, binaryData);
  }

  DeleteFiles(
    file_id: number,
  ): Observable<{ status: string; message: string }> {
    const headers = this.getHeaders();
    return this.http
      .delete<{
        status: string;
        message: string;
      }>(`${environment.apiUrl}files/${file_id}/`, { headers })
      .pipe();
  }
}
