.hidden-on-hover {
  display: none;
}

.file-item:hover .hidden-on-hover {
  display: inline-block;
}

/* Style the entire file item row */
.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 4px 8px;
  border-radius: 4px;
  position: relative;
}

/* Show controls only when hovering over file-item */
.file-item:hover .hidden-on-hover {
  display: flex;
}

/* Light hover effect for background */
.file-item:hover {
  background-color: #f5f5f5;
}

/* The section containing "View File" and delete icon */
.hidden-on-hover {
  display: none;
  align-items: center;
  margin-left: auto;
}

/* View File button styling */
.view-file-btn {
  background-color: #f0f0f0;
  color: #1976d2;
  font-size: 12px;
  padding: 2px 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 8px;
}

/* Hover effect for the button */
.view-file-btn:hover {
  background-color: #e0e0e0;
}

/* Delete icon styling */
.delete-icon {
  color: #d32f2f;
  cursor: pointer;
  font-size: 20px;
}

mat-tree-node {
  display: flex;
  align-items: center;
  height: 40px;
  padding-right: 10px;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
}

.folder-name {
  flex: 1;
  font-weight: 500;
  margin-left: 5px;
}

.folder-controls {
  display: flex;
  align-items: center;
  margin-left: auto;
}

.view-file-btn {
  background-color: #f5f5f5;
  border: none;
  padding: 4px 10px;
  font-size: 12px;
  border-radius: 12px;
  cursor: pointer;
}

.hidden-on-hover {
  display: flex;
  gap: 10px;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
}

mat-tree-node:hover .hidden-on-hover,
mat-tree-node:hover .folder-controls {
  opacity: 1;
}

.icon-button {
  padding: 0;
  width: 32px;
  height: 32px;
}

.arrow-button {
  margin-right: 4px;
  color: #555;
}

.selected-file {
  background-color: #f2f0fc;
}

.selected-folder {
  background-color: #255d9d;
  color: white;
  border-radius: 10px;
  margin: 4px 0;
  /* padding: 8px; */
  font-weight: 500;
}

.example-tree-progress-bar {
  margin: 10px;
}
