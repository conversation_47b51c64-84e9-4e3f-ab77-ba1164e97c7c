import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Output } from '@angular/core';
import { MatOption } from '@angular/material/core';
import { MatIcon } from '@angular/material/icon';
import { MatSelect } from '@angular/material/select';
import { SharedModule } from '../../../../shared/shared.module';
import { FormsModule } from '@angular/forms';
import { SortOptions } from '../models';

@Component({
  selector: 'app-data-view-sort',
  imports: [
    MatOption,
    MatIcon,
    CommonModule,
    MatSelect,
    SharedModule,
    FormsModule,
  ],
  templateUrl: './data-view-sort.component.html',
  styleUrl: './data-view-sort.component.css',
})
export class DataViewSortComponent {
  selectedSortOption = 'initialSelect'; // Default selected option
  sortDropdownOptions = SortOptions;
  @Output() sortOptionChanged = new EventEmitter<string>(); // New Output for selectedSortOption

  onSortOptionChange(selectedSortOption: string): void {
    if (selectedSortOption === '') {
      this.selectedSortOption = 'initialSelect'; // Reset the sort if "Remove sort" is selected
    } else {
      this.selectedSortOption = selectedSortOption;
    }
    this.sortOptionChanged.emit(this.selectedSortOption);
  }
}
