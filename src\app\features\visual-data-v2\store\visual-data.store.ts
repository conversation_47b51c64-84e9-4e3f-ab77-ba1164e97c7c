import {
  patchState,
  signalStore,
  withHooks,
  withMethods,
  withState,
} from '@ngrx/signals';
import { inject } from '@angular/core';
import { rxMethod } from '@ngrx/signals/rxjs-interop';
import { map, pipe, switchMap, tap } from 'rxjs';
import { PlotService } from '../services/plot.service';
import { VisualDataState } from './visual-data.state';
import { withDevtools } from '@angular-architects/ngrx-toolkit';
import { Router } from '@angular/router';
import { GridsterItemWithPlotly, PlotDataUI } from '../models/plot.model';
import { SearchOptions } from '../../../_models/common.model';
import { CreatePlotPayload } from '../components/plot-creation-modal/plot-creation-models';

const initialState: VisualDataState = {
  loadedPlotsData: null,
  isLoading: false,
  filter: {},
  error: null,
  projectData: null,
  pagination: {
    currentPage: 0,
    pageSize: 7, // Default page size
  },
  showFavouritePlots: false,
};

export const visualDataStore = signalStore(
  { providedIn: 'root' },
  withState<VisualDataState>(initialState),

  // DevTools integration
  withDevtools('VisualDataStore'),

  // FOR STATE MANIPULATION
  withMethods(store => ({
    _setLoadingPlotData() {
      patchState(store, {
        isLoading: true,
        error: null,
        loadedPlotsData: null,
      });
    },
    _setError(error: Error) {
      patchState(store, {
        isLoading: false,
        error: error,
      });
    },
    _setLoadedPlotData(data: VisualDataState['loadedPlotsData']) {
      patchState(store, {
        loadedPlotsData: data,
        isLoading: false,
        error: null,
      });
    },
    _setLoadingScreen(value: boolean) {
      patchState(store, {
        isLoading: value,
      });
    },
    _setUpdatedGridLayout(updatedItems: GridsterItemWithPlotly[]) {
      const currentData = store.loadedPlotsData();

      if (!currentData) return;

      const updatedData = currentData.plots.map(plot => {
        const match = updatedItems.find(item => item.plot_id === plot.id);
        return match
          ? ({
              ...plot,
              display_layout: { ...match } as GridsterItemWithPlotly,
            } as PlotDataUI)
          : plot;
      });
      patchState(store, {
        loadedPlotsData: {
          ...currentData,
          plots: updatedData,
        },
      });
    },

    _toggleFavouritePlots() {
      const currentState = store.showFavouritePlots();
      patchState(store, {
        showFavouritePlots: !currentState,
      });
    },
    _markPlotAsFavouriteInStore(plotId: number, isFav: boolean) {
      const currentData = store.loadedPlotsData();
      if (!currentData) return;
      const updatedData = currentData.plots.map(plot => {
        return plot.id === plotId
          ? ({
              ...plot,
              favorite: isFav,
            } as PlotDataUI)
          : plot;
      });
      patchState(store, {
        loadedPlotsData: {
          ...currentData,
          plots: updatedData,
        },
      });
    },

    _removePlotFromStore(plotId: number) {
      const currentData = store.loadedPlotsData();
      if (!currentData) return;
      const updatedData = currentData.plots.filter(plot => plot.id !== plotId);
      patchState(store, {
        loadedPlotsData: {
          ...currentData,
          plots: updatedData,
        },
      });
    },
  })),

  // FOR ASYNC CALLS
  withMethods((store, plotService = inject(PlotService)) => ({
    loadUserPlotData: rxMethod<{
      currentPage?: number;
      searchOptions?: SearchOptions;
      favouritesOnly?: boolean;
    }>(
      pipe(
        tap(() =>
          patchState(store, {
            isLoading: true,
            error: null,
            loadedPlotsData: null,
          }),
        ),
        switchMap(({ currentPage = 0, searchOptions, favouritesOnly }) => {
          const projectId = Number(store.projectData()?.id);
          let payload = `cursor=${currentPage}&limit=${store.pagination.pageSize()}`;

          /* 
          TO-DO : 
          Need Better way to handle this in API, 
          favorite param in API has 3 meaning but its API input is boolean and boolean holds only 2 meaning
          which is not correct. it should be enum or string
          NOTE : For API favouritesOnly has 3 meaning :
            if we sent &favorite == true => show only favourite plots
            if we sent &favorite == false => show only non-favourite plots
            if we sent &favorite == null => show all plots
          */
          if (favouritesOnly === true) {
            payload += `&favorite=${favouritesOnly}`;
          } else if (favouritesOnly === false) {
            payload += `&favorite=${favouritesOnly}`;
          } else {
            // Do nothing : Do dont add favourite param in API, hence all plots will be shown
          }

          if (searchOptions?.title) {
            payload += `&search_text=${searchOptions.title}`;
          }

          if (searchOptions?.minDate) {
            payload += `&min_selection_date=${searchOptions.minDate}`;
          }

          if (searchOptions?.maxDate) {
            payload += `&max_selection_date=${searchOptions.maxDate}`;
          }
          return plotService.getCustomPlots(projectId, payload).pipe(
            map(response => {
              const allPlotsData = response.data;
              const transformedDataForUI =
                plotService.transformPlotDataResponseToPlotDataUI(allPlotsData);
              return transformedDataForUI;
            }),
          );
        }),
        tap({
          next: data => {
            store._setLoadedPlotData(data);
          },
          error: error => {
            store._setError(error);
          },
        }),
      ),
    ),

    saveDisplayLayoutInDbAndSyncStoreLayoutWithGridster: rxMethod<{
      projectId: number;
      changedItemLayout: GridsterItemWithPlotly[];
    }>(
      pipe(
        switchMap(({ projectId, changedItemLayout }) =>
          plotService.savePlotLayoutData(projectId, changedItemLayout).pipe(
            map(response => {
              const updatedItems = response.data;
              const transformedUpdatedItems = updatedItems.map(item => {
                return {
                  plot_id: item.plot_id,
                  ...item.display_layout,
                } as GridsterItemWithPlotly;
              });
              return transformedUpdatedItems;
            }),
          ),
        ),
        tap({
          next: updatedItems => {
            store._setUpdatedGridLayout(updatedItems);
          },
          error: error => {
            store._setError(error);
          },
        }),
      ),
    ),

    async markUnmarkPlotFavPlot(plotId: number, isFav: boolean): Promise<void> {
      try {
        await plotService.markUnmarkPlotFavPlot(plotId, isFav);
        store._markPlotAsFavouriteInStore(plotId, isFav);
        if (store.showFavouritePlots() && isFav === false) {
          /* If the user is viewing favourite plots and the plot is unmarked as favourite,
           remove it from the UI i.e from store
          */
          store._removePlotFromStore(plotId);
        }
      } catch (err) {
        const error =
          err instanceof Error
            ? err
            : new Error('Unknown Error', { cause: err });
        store._setError(error);
      }
    },

    async removePlotFromDb(plotId: number): Promise<void> {
      try {
        store._setLoadingScreen(true);
        await plotService.removePlotFromDb(plotId);
        store._removePlotFromStore(plotId);
        store._setLoadingScreen(false);
      } catch (err) {
        const error =
          err instanceof Error
            ? err
            : new Error('Unknown Error', { cause: err });
        store._setError(error);
      }
    },
    async addNewPlotToDb(
      fileId: number,
      payload: CreatePlotPayload,
    ): Promise<void> {
      try {
        store._setLoadingScreen(true);
        await plotService.setNewPlotAsync(fileId, payload);
        store._setLoadingScreen(false);
      } catch (err) {
        const error =
          err instanceof Error
            ? err
            : new Error('Unknown Error', { cause: err });
        store._setError(error);
      }
    },
    async getProjectData(): Promise<void> {
      try {
        const router = inject(Router);
        const snapshot = router.routerState.snapshot;

        let currentRoute = snapshot.root;
        while (currentRoute.firstChild) {
          currentRoute = currentRoute.firstChild;
        }

        const projectID =
          currentRoute.params['id'] ?? currentRoute.parent?.params['id'] ?? '';

        const response = await plotService.getProjectByIdAsync(projectID);
        patchState(store, {
          projectData: response.data,
        });
      } catch (err) {
        const error =
          err instanceof Error
            ? err
            : new Error('Unknown Error', { cause: err });
        store._setError(error);
      }
    },
  })),

  // Operations related to search
  withMethods(store => ({
    async toggleFavouritePlots(): Promise<void> {
      try {
        const currentFavouriteState = store.showFavouritePlots();

        await store.loadUserPlotData({
          currentPage: 0,

          // CAUSION : we are handling favouritesOnly like this becuase "false" dont mean to show all plots
          favouritesOnly: currentFavouriteState ? undefined : true,
        });
        store._toggleFavouritePlots();
      } catch (err) {
        const error =
          err instanceof Error
            ? err
            : new Error('Unknown Error', { cause: err });
        store._setError(error);
      }
    },
  })),

  // Hooks
  withHooks({
    onInit: async store => {
      // LOAD PROJECT DATA
      await store.getProjectData();

      // MAKE API CALL
      store.loadUserPlotData({ currentPage: store.pagination.currentPage() });
    },
  }),
);
