import {
  DataViewFile,
  DirectoryEntry,
  FileDirectoryEntry,
  FileEntry,
} from './data-view.interfaces';

export function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const base64 = (reader.result as string).split(',')[1];
      resolve(base64);
    };
    reader.onerror = error => reject(error);
    reader.readAsDataURL(file);
  });
}

export function addFileToDirectory(
  directory: DirectoryEntry,
  relativePath: string,
  base64: string,
  directoryId: number,
) {
  const parts = relativePath.split('/');
  let currentDirectory = directory;

  for (const part of parts.slice(0, -1)) {
    let subDir = currentDirectory.children.find(
      child => child.type === 'directory' && child.name === part,
    ) as DirectoryEntry;
    if (!subDir) {
      subDir = {
        id: directoryId++,
        type: 'directory',
        name: part,
        children: [],
      };
      currentDirectory.children.push(subDir);
    }
    currentDirectory = subDir;
  }

  currentDirectory.children.push({
    id: directoryId++,
    type: 'file',
    name: parts[parts.length - 1],
    path: base64,
  });
}

export async function getFileBlob(
  fileEntry: FileEntry | DirectoryEntry,
): Promise<Blob | null> {
  console.log(fileEntry);
  if (fileEntry && fileEntry.path) {
    const base64Data = fileEntry.path;
    const fileName = fileEntry.name;
    try {
      const fileExtension = fileName.split('.').pop()?.toLowerCase();
      let mimeType = 'application/octet-stream';
      switch (fileExtension) {
        case 'csv':
          mimeType = 'text/csv';
          break;
        case 'parquet':
          mimeType = 'application/json';
          break;
        case 'xml':
          mimeType = 'application/xml';
          break;
        case 'yaml':
        case 'yml':
          mimeType = 'application/x-yaml';
          break;
        case 'tsv':
          mimeType = 'text/tab-separated-values';
          break;
        case 'txt':
          mimeType = 'text/plain';
          break;
        case 'png':
          mimeType = 'image/png';
          break;
        case 'jpeg':
        case 'jpg':
          mimeType = 'image/jpeg';
          break;
        default:
          mimeType = 'application/octet-stream';
      }
      const byteCharacters = atob(base64Data);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: mimeType });

      return blob;
    } catch (error) {
      console.error('Error creating Blob from base64:', error);
      return null;
    }
  }
  return null;
}

export function readFileAsArrayBuffer(file: File): Promise<ArrayBuffer> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as ArrayBuffer);
    reader.onerror = reject;
    reader.readAsArrayBuffer(file);
  });
}

export function convertFileEntryToFile(
  fileEntry: FileSystemFileEntry,
): Promise<DataViewFile> {
  return new Promise<DataViewFile>((resolve, reject) => {
    try {
      fileEntry.file(file =>
        resolve({
          file,
          fullPath:
            fileEntry?.fullPath
              ?.replace(/^\/+/, '')
              .split('/')
              .slice(0, -1)
              .join('/') ?? '',
        }),
      );
    } catch (error) {
      reject(error);
    }
  });
}

export function updateHierarchy(
  existingFiles: FileDirectoryEntry[],
  fullPath: string,
  incomingFiles: FileDirectoryEntry[],
) {
  const pathParts = fullPath.split('/');

  function recurse(
    nodes: FileDirectoryEntry[],
    parts: string[],
    currentPath = '',
  ) {
    return nodes.map((node: FileDirectoryEntry): FileDirectoryEntry => {
      const fullNodePath = currentPath
        ? `${currentPath}/${node.name}`
        : node.name;
      if (
        node.type === 'folder' &&
        fullNodePath ===
          pathParts.slice(0, fullNodePath.split('/').length).join('/')
      ) {
        if (fullNodePath === fullPath) {
          // Merge children without duplicates (by name)
          const existingChildren = node.children || [];

          // Filter incomingFiles to only those not in existingChildren by name
          const newChildren = incomingFiles.filter(
            incoming =>
              !existingChildren.some(child => child.name === incoming.name),
          );

          return {
            ...node,
            children: [...existingChildren, ...newChildren],
          };
        } else {
          return {
            ...node,
            children: recurse(node.children || [], parts, fullNodePath),
          };
        }
      } else {
        return node;
      }
    });
  }

  return recurse(existingFiles, pathParts);
}
