import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  effect,
  inject,
} from '@angular/core';
import { SharedModule } from '../../../../shared/shared.module';
import { MatButtonModule } from '@angular/material/button';
import { DataViewSortComponent } from '../../shared/data-view-sort/data-view-sort.component';
import { MatIcon } from '@angular/material/icon';
import { DataViewTreeComponent } from '../../shared/data-view-tree/data-view-tree.component';
import { DataViewComponentStore } from '../../component-store/data-view.store';
import { CommonModule } from '@angular/common';
import { MatDialog } from '@angular/material/dialog';
import { DataViewFileUploadComponent } from '../data-view-file-upload/data-view-file-upload.component';
import { FileDirectoryEntry } from '../../shared/data-view.interfaces';

@Component({
  selector: 'app-data-view-landing',
  imports: [
    SharedModule,
    MatIcon,
    MatButtonModule,
    DataViewSortComponent,
    DataViewTreeComponent,
    CommonModule,
  ],
  templateUrl: './data-view-landing.component.html',
  standalone: true,
  providers: [DataViewComponentStore],
  styleUrl: './data-view-landing.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DataViewLandingComponent {
  readonly dialog = inject(MatDialog);
  constructor(private cdr: ChangeDetectorRef) {}
  readonly store = inject(DataViewComponentStore);
  dataViewHierarchy: Record<number, FileDirectoryEntry[]> = {};
  title = 'Project Details';
  isLoading = true;
  projectId = localStorage.getItem('project_id') || 0;
  selected_id = 0;
  folder_id = localStorage.getItem('folder_id');

  // ngOnInit(): void {
  // const project_id = localStorage.getItem("project_id")
  // }
  logHierarchyAfterStoreInit = effect(() => {
    if (!this.store.isLoading()) {
      const hierarchy = this.store.hierarchy();
      this.dataViewHierarchy = hierarchy;
      this.isLoading = false;
      this.cdr.markForCheck();
    }
  });

  uploadFileFolder() {
    this.dialog.open(DataViewFileUploadComponent, {
      ...DataViewFileUploadComponent.viewConfig,
    });
  }

  sortOptionChanged(sortvalue: string) {
    this.store.setSortValue(sortvalue);
    this.store.loadFiles(this.store.project_id());
  }
}
