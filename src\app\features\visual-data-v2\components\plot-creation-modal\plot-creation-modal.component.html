@if (this.loading) {
  <div class="flex justify-center items-center p-5">
    <mat-spinner></mat-spinner>
  </div>
} @else {
  <div class="h-full w-full">
    <div class="overflow-y-auto px-3">
      <h2 mat-dialog-title>New Plot</h2>
      <hr />
      <div mat-dialog-content>
        <form [formGroup]="plotCreationForm">
          <div class="flex flex-row justify-between">
            <!-- PLOT TYPE SELECTION -->
            <div>
              <h3>Plot Type</h3>
              <mat-form-field>
                <mat-select
                  formControlName="plotType"
                  (selectionChange)="onPlotTypeSelectionChange($event)">
                  @for (group of plotTypesArray(); track group.key) {
                    <mat-optgroup [label]="group.key">
                      @for (plot of group.values; track plot.plot_name) {
                        <mat-option [value]="plot.plot_name">
                          {{ plot.plot_name }}
                        </mat-option>
                      }
                    </mat-optgroup>
                  }
                </mat-select>
              </mat-form-field>
            </div>
            <!-- Switchs Setting buttons-->
            <div>
              @for (
                switchSettings of this.availableSwitchSettingsArray();
                track switchSettings
              ) {
                <mat-button-toggle-group
                  hideSingleSelectionIndicator="true"
                  [value]="switchSettings.toggleOptions[0]">
                  @for (
                    toggleOpt of switchSettings.toggleOptions;
                    track toggleOpt
                  ) {
                    <mat-button-toggle
                      [value]="toggleOpt"
                      (change)="
                        onSwitchSettingSelectionChange(
                          $event,
                          switchSettings.setting_name
                        )
                      ">
                      {{ toggleOpt }}
                    </mat-button-toggle>
                  }
                </mat-button-toggle-group>
              }
            </div>
          </div>

          <!-- FILE SELECTION -->
          <div>
            <h3>Data Source</h3>
            <app-data-source-input
              (selectionEmitted)="handleFileSelectionChange($event)">
            </app-data-source-input>
          </div>
          <!-- Option set -->
          <div formArrayName="selectedOptions">
            <h3>Options Configuration</h3>
            @for (
              optSet of getSelectedOptionSetArray().controls;
              let optIndex = $index;
              track optSet
            ) {
              <div [formGroupName]="optIndex">
                <p>{{ optSet.get('optionName')?.value }}</p>
                <!-- Smart-bucking :  Option set has only one Smart bucketing strategy hence it is not under optionSet.values but property on optionSet.selectedSmartBucket-->
                @if (optSet.get('canSmartBucketed')?.value === true) {
                  <div>
                    <mat-form-field appearance="fill">
                      <mat-label>Smart Bucketing</mat-label>
                      <mat-select [formControlName]="'selectedSmartBucket'">
                        @for (
                          option of availableSmartBucketChoices;
                          track option.smart_bucketing_value
                        ) {
                          <mat-option [value]="option">
                            {{ option.smart_bucketing_name }}
                          </mat-option>
                        }
                      </mat-select>
                    </mat-form-field>
                  </div>
                }

                <div formArrayName="values">
                  @for (
                    val of getValuesArrayFromOptionSet(optSet).controls;
                    let valIndex = $index;
                    track val
                  ) {
                    <div
                      [formGroupName]="valIndex"
                      class="flex items-center gap-2 my-2">
                      <mat-form-field appearance="fill">
                        <mat-label>Column name</mat-label>
                        <mat-select [formControlName]="'selectedColName'">
                          @for (
                            availableColChoice of this.getColumnNameFromOptionSetBasedOnOptSetFormControl(
                              optSet
                            );
                            track availableColChoice
                          ) {
                            <mat-option [value]="availableColChoice">
                              {{ availableColChoice }}
                            </mat-option>
                          }
                        </mat-select>
                      </mat-form-field>
                      <!-- Aggregation Set -->
                      @if (optSet.get('aggregations')?.value) {
                        <mat-form-field appearance="fill">
                          <mat-label>Aggregation</mat-label>
                          <mat-select
                            [formControlName]="'selectedArggregation'">
                            @for (
                              option of availableAggregationChoices;
                              track option
                            ) {
                              <mat-option [value]="option">
                                {{ option }}
                              </mat-option>
                            }
                          </mat-select>
                        </mat-form-field>
                      }

                      <!-- Conditionally show Add or Remove button -->
                      @if (optSet.get('multiple')?.value === true) {
                        @if (valIndex === 0) {
                          <button
                            type="button"
                            (click)="addOptionValueInOptionSet(optSet)"
                            class="btn btn-sm btn-outline">
                            +
                          </button>
                        } @else {
                          <button
                            type="button"
                            (click)="
                              removeOptionValueFromOptionSet(optSet, valIndex)
                            "
                            class="btn btn-sm btn-outline text-red-500">
                            ×
                          </button>
                        }
                      }
                    </div>
                  }
                </div>
              </div>
            }
          </div>

          <!-- Settings -->
          <div formArrayName="selectedSettings">
            <h3>Settings</h3>
            <div>
              <!-- Dropdown Settings -->
              <mat-form-field>
                <!-- Below  [multiple] cant be change dyanmically once initialize-->
                <mat-select [multiple]="true" [placeholder]="'Select Settings'">
                  @for (
                    dropDownSetting of this.availableDropdownSettingsArray();
                    track dropDownSetting
                  ) {
                    <mat-option
                      [value]="dropDownSetting"
                      (onSelectionChange)="
                        onDropDownSettingSelectionChange($event)
                      ">
                      {{ dropDownSetting }}
                    </mat-option>
                  }
                </mat-select>
              </mat-form-field>
            </div>
          </div>
        </form>
      </div>

      <div mat-dialog-actions>
        <button
          mat-button
          (click)="closePlotCreationModal()"
          type="button"
          class="text-gray-400 hover:text-gray-600">
          Cancel
        </button>

        <button
          mat-button
          (click)="onSubmit()"
          type="submit"
          class="text-gray-400 hover:text-gray-600">
          Submit
        </button>
      </div>
    </div>
  </div>
}
