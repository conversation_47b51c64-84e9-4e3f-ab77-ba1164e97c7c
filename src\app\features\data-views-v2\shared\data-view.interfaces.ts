import { PaginationInfo } from '../../../_models/visual-data/visual-data.model';

export interface FlatNode {
  name: string;
  level: number;
  expandable: boolean;
}

export interface PresignedUrlPayload {
  file_name: string;
  size: number;
}

export interface ProcessFilePayload {
  file_name: string;
  file_key: string;
  folder: string;
  project_id: number;
  size: number;
}

export interface ProcessFileResponse {
  data: {
    task_id: string;
    file_id: number;
  };
  errors: Error;
  message: string;
  pagination: PaginationInfo;
  status: string;
}

export interface PresignedUrlResponse {
  file_key: string;
  signed_url: string;
}

export interface DataViewResponse<T> {
  data: T[];
  errors: Error;
  message: string;
  pagination: PaginationInfo;
  status: string;
}

export interface DirectoryEntry {
  type?: string;
  name: string;
  path?: string;
  id?: number;
  children: (DirectoryEntry | FileEntry)[];
}

export interface FileEntry {
  id?: number;
  type: string;
  name: string;
  path?: string;
}

export interface FileDirectoryEntry {
  type?: string;
  name: string;
  path?: string;
  id?: number;
  children?: FileDirectoryEntry[];
}

export interface UploadedFile {
  file: DataViewFile;
  progress: number;
  uploading: boolean;
}

export interface DataViewFile {
  file: File;
  fullPath: string;
}

export interface DataviewResponse {
  data: {
    files: { id: number; name: string; folder: string }[];
    folder: string;
    pagination: {
      current_page: number;
      has_next: boolean;
      has_previous: boolean;
      total_items: number;
      total_pages: number;
    };
    subfolders: string[];
  };
  errors: Error;
  message: string;
  pagination: PaginationInfo;
  status: string;
}

export interface ProcessStatusResponse {
  status: string;
  message: string;
  data: {
    status: string;
  };
  errors: Error;
  pagination: PaginationInfo;
}
