import { Component, Input } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { DataViewUploadTableHeading, FileIcon } from '../../../shared/models';
import { UploadedFile } from '../../../shared/data-view.interfaces';

@Component({
  selector: 'app-upload-files-table',
  imports: [MatIconModule],
  templateUrl: './upload-files-table.component.html',
  styleUrl: './upload-files-table.component.css',
})
export class UploadFilesTableComponent {
  @Input() uploadedFilesList: UploadedFile[] = [];
  tableHeading = DataViewUploadTableHeading;

  openFileSelector() {
    console.log('open fileselecteor');
  }

  getFileIcons(file: File): string {
    if (file.name.endsWith('.csv')) return FileIcon.CSV;
    else if (file.name.endsWith('.parquet')) return FileIcon.PARQUET;
    else if (file.type.startsWith('image/')) return FileIcon.IMAGE;
    else return FileIcon.DEFAULT;
  }

  removeFile(i: number) {
    console.log('remove file', i);
  }

  trackByIndex(index: number): number {
    return index;
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
