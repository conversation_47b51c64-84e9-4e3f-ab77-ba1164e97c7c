<div class="hidden md:block">
  <mat-select
    [(ngModel)]="selectedSortOption"
    (selectionChange)="onSortOptionChange($event.value)"
    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 w-[230px]">
    <!-- Show the 'Select an option to sort' only if no option is selected -->
    <mat-option
      *ngIf="!selectedSortOption || selectedSortOption === 'initialSelect'"
      value="initialSelect"
      disabled>
      Select an option to sort
    </mat-option>

    <!-- Show sorting options -->
    @for (option of sortDropdownOptions; track option) {
      <mat-option [value]="option.value">
        <span>Sorted by {{ option.title }}</span>
        <mat-icon>import_export</mat-icon>
      </mat-option>
    }

    <!-- Show 'Remove sort' only if an option is selected -->
    <mat-option
      *ngIf="selectedSortOption && selectedSortOption !== 'initialSelect'"
      value="">
      Remove sort
    </mat-option>
  </mat-select>
</div>
