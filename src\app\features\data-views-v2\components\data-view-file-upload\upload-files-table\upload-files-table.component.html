<div class="table-container">
  <div class="max-h-[300px] overflow-y-auto">
    <table class="upload-files-table">
      <thead>
        <tr>
          @for (heading of tableHeading; track heading) {
            <th>{{ heading }}</th>
          }
        </tr>
      </thead>
      <tbody>
        @for (
          file of uploadedFilesList;
          track trackByIndex($index);
          let i = $index
        ) {
          <tr [attr.data-index]="i">
            <td>
              <div class="flex items-center">
                <mat-icon
                  class="file-icon"
                  [fontSet]="
                    getFileIcons(file.file.file) !== 'image'
                      ? 'material-icons-outlined'
                      : ''
                  "
                  >{{ getFileIcons(file.file.file) }}</mat-icon
                >
                <span class="file-name">{{
                  file.file.file.name
                }}</span>
              </div>
            </td>
            <td>
              {{ formatFileSize(file.file.file.size) }}
            </td>
            <td>
              <button
                mat-icon-button
                (click)="removeFile(i)"
                [disabled]="file.uploading"
                class="remove-button text-red-500 hover:text-red-700 hover:bg-red-50 transition-colors duration-200"
                aria-label="Remove file">
                <mat-icon>delete_outline</mat-icon>
              </button>
            </td>
          </tr>
        }
      </tbody>
    </table>
  </div>
</div>
