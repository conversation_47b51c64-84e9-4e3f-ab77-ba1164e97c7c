<div *ngIf="!isLoading">
  <app-component-header [projectName]="title">
    <button
      mat-flat-button
      class="whitespace-nowrap text-ellipsis add-button"
      (click)="uploadFileFolder()">
      <mat-icon>add</mat-icon>
      Add Data
    </button>
    <app-data-view-sort
      class="sort"
      (sortOptionChanged)="sortOptionChanged($event)"></app-data-view-sort>
  </app-component-header>
  <!-- {{dataViewHierarchy | json}} -->
  <div class="p-4 flex flex-row mt-[28px]">
    <div class="w-full overflow-y-auto max-h-[80vh]">
      <app-data-view-tree [folderData]="dataViewHierarchy"></app-data-view-tree>
    </div>
  </div>
</div>
